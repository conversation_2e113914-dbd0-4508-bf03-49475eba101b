# Contributing to Day.js

Thank you for taking the time to read our contribution guidelines. You can start contributing in many ways like filing bug reports, [improving the locale && documentation](https://github.com/iamkun/dayjs/issues/171), or helping others.

Our open source community strives to be nice, welcoming and professional. Instances of abusive, harassing, or otherwise unacceptable behavior will not be tolerated.

## Style

* Day.js is written in ES6.
* We use ESLint to check our code. You can use `npm run lint` before submitting a pull request.
* Please use semantic commit message.

## Bugs

* Before submitting a bug report, search the issues for similar tickets. Your issue may have already been discussed and resolved. 
* Feel free to add a comment to an existing issue, even if it's closed.
* Be thorough in your title and report, don't leave out important details.
* English, please.

## Tests

* Feel free to create a new `test/*.test.js` file if none of the existing test files suits your test case.
* Help us keeping 100% test coverage :D.
* You can use `npm run test` before submitting a pull request.
