// Irish or Irish Gaelic [ga]
import dayjs from 'dayjs'

const locale = {
  name: 'ga',
  weekdays: '<PERSON><PERSON>_<PERSON><PERSON>_Dé <PERSON>_Dé <PERSON>_Déardaoin_Dé hAoine_Dé <PERSON>'.split('_'),
  months: '<PERSON><PERSON><PERSON><PERSON>_<PERSON>_Márta_Aibreán_Bealtaine_Méitheamh_Iúil_Lúnasa_Meán <PERSON>hai<PERSON>_<PERSON>aireadh Fómhair_<PERSON>hain_<PERSON>ig'.split('_'),
  weekStart: 1,
  weekdaysShort: 'Dom_Lua_Mái_Céa_Déa_hAo_Sat'.split('_'),
  monthsShort: 'Ean<PERSON>_Feab_Márt_Aibr_Beal_Méit_Iúil_Lúna_Meán_Deai_Samh_Noll'.split('_'),
  weekdaysMin: 'Do_Lu_Má_Ce_Dé_hA_Sa'.split('_'),
  ordinal: n => n,
  formats: {
    LT: 'HH:mm',
    LTS: 'HH:mm:ss',
    L: 'DD/MM/YYYY',
    LL: 'D MMMM YYYY',
    LLL: 'D MMMM YYYY HH:mm',
    LLLL: 'dddd, D MMMM YYYY HH:mm'
  },
  relativeTime: {
    future: 'i %s',
    past: '%s ó shin',
    s: 'cúpla soicind',
    m: 'nóiméad',
    mm: '%d nóiméad',
    h: 'uair an chloig',
    hh: '%d uair an chloig',
    d: 'lá',
    dd: '%d lá',
    M: 'mí',
    MM: '%d mí',
    y: 'bliain',
    yy: '%d bliain'
  }
}

dayjs.locale(locale, null, true)

export default locale

