name: Lint & Unit Test
on:
  push:
    branches: [ master, dev ]
  pull_request:
    branches: [dev]
jobs:
  check:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node: [ 12, 14, 16, 18 ]
    steps:
    - name: Checkout
      uses: actions/checkout@v3
    - name: Setup node
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    - name: Install dependencies
      run: npm install -g codecov && npm install
    - name: Run Lint
      run: npm run lint
    - name: Run tests
      run: npm test && codecov 
